'use client';

import { ChartAreaInteractive } from '@/components/chart-area-interactive';
import { DataTable } from '@/components/data-table';
import { SectionCards } from '@/components/section-cards';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { AlertCircle, BarChart3, Database, TrendingUp } from 'lucide-react';
import { DashboardDevicesOverview } from '@/components/dashboard-devices-overview';

export default function DashboardPage() {
  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      {/* Basic dashboard access - all authenticated users can view */}
      <OrgPermissionGuard
        permission={Permission.ViewProfile}
        fallback={
          <div className="flex items-center gap-3 p-6 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <div>
              <h3 className="font-semibold text-red-800">Access Denied</h3>
              <p className="text-red-600 text-sm">
                You need basic access permissions to view the dashboard.
              </p>
            </div>
          </div>
        }
      >
        <SectionCards />
      </OrgPermissionGuard>

      {/* Analytics and charts - requires view permissions */}
      <OrgPermissionGuard
        permission={[Permission.ViewDevices, Permission.ViewJobs]}
        fallback={
          <div className="flex items-center gap-3 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
            <BarChart3 className="h-5 w-5 text-yellow-500" />
            <div>
              <h3 className="font-semibold text-yellow-800">Limited Access</h3>
              <p className="text-yellow-600 text-sm">
                Device or job view permissions required to see analytics.
              </p>
            </div>
          </div>
        }
      >
        <div className="px-4 lg:px-6">
          <ChartAreaInteractive />
        </div>
      </OrgPermissionGuard>

      {/* Data table - requires view permissions */}
      <OrgPermissionGuard
        permission={Permission.ViewJobs}
        fallback={
          <div className="flex items-center gap-3 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <Database className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="font-semibold text-blue-800">Data Access Required</h3>
              <p className="text-blue-600 text-sm">
                Job view permissions required to see detailed data tables.
              </p>
            </div>
          </div>
        }
      >
        <DataTable />
        <DashboardDevicesOverview />
      </OrgPermissionGuard>
    </div>
  );
}

import axios, { AxiosInstance, AxiosError } from 'axios';
import { getCognitoTokenCookies, getUserEmailFromToken, getCurrentSessionId } from '@/lib/session';
import { getLogger } from '@/lib/logger';

// Determine base URL based on environment - Always use QBraid API
const env = process.env.NODE_ENV;
const customEnv = process.env.NEXT_PUBLIC_NODE_ENV;
const baseURL = 'https://api.qbraid.com/api'; // Default to staging instead of localhost

// Types for API responses and errors
interface APIErrorResponse {
  message?: string;
  error?: string;
  [key: string]: unknown;
}

/**
 * Server-side HTTP client for external QBraid API
 * Handles authentication and request/response transformations
 */
class ExternalAPIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: { 'Content-Type': 'application/json' },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor for auth with session-aware token retrieval
    this.client.interceptors.request.use(
      async (config) => {
        try {
          // Get current session ID for Redis-based token retrieval
          const sessionId = await getCurrentSessionId();
          const tokens = await getCognitoTokenCookies(sessionId || undefined);
          const userEmail = await getUserEmailFromToken();

          // Enhanced: Check for token mismatch and handle it
          if (sessionId && !tokens.accessToken && !tokens.idToken) {
            const logger = getLogger();
            logger.warn(
              `⚠️ [API-CLIENT] No Cognito tokens found for session ${sessionId.substring(0, 10)}..., checking for mismatch`,
            );

            // Import validateSessionConsistency dynamically to avoid circular imports
            const { validateSessionConsistency } = await import('@/lib/session');
            const validation = await validateSessionConsistency(sessionId);

            if (validation.hasMismatch && !validation.canRecover) {
              logger.error(
                `❌ [API-CLIENT] Unrecoverable token mismatch detected, clearing session`,
              );

              // Clear session to force re-authentication
              const { clearSession } = await import('@/lib/session');
              await clearSession(sessionId);

              throw new Error(
                'Session invalidated due to token mismatch - please re-authentication',
              );
            }
          }

          // Priority: ID Token → Access Token → API Key (following auth wall guide)
          if (tokens.idToken) {
            // Option 1: ID Token (Recommended)
            config.headers['id-token'] = tokens.idToken;
            if (userEmail) {
              config.headers['email'] = userEmail; // optional with ID token
            }
            config.headers['domain'] = 'qbraid'; // optional
            const logger = getLogger();
            logger.info(`🔐 [API-CLIENT] Using ID token from ${sessionId ? 'Redis' : 'cookies'}`);
          } else if (tokens.accessToken) {
            // Option 2: Access Token (Fallback)
            config.headers['access-token'] = tokens.accessToken;
            if (userEmail) {
              config.headers['email'] = userEmail; // REQUIRED with access token
            }
            config.headers['domain'] = 'qbraid'; // optional
            const logger = getLogger();
            logger.info(
              `🔐 [API-CLIENT] Using access token fallback from ${sessionId ? 'Redis' : 'cookies'}`,
            );
          } else if (process.env.QBRAID_API_TOKEN) {
            // Option 3: API Key (Server-to-server fallback)
            config.headers['api-key'] = process.env.QBRAID_API_TOKEN;
            config.headers['domain'] = 'qbraid'; // optional
            const logger = getLogger();
            logger.info('🔐 [API-CLIENT] Using API key from environment');
          } else {
            // No authentication available - this should trigger re-authentication
            const logger = getLogger();
            logger.warn('⚠️ [API-CLIENT] No authentication tokens available - API call may fail');
            throw new Error('No authentication tokens available');
          }

          // Always try to include email for better security
          if (userEmail) {
            config.headers['email'] = userEmail;
          } else if (process.env.QBRAID_API_EMAIL) {
            config.headers['email'] = process.env.QBRAID_API_EMAIL;
          }

          const logger = getLogger();
          logger.info(`🔄 [External API] ${config.method?.toUpperCase()} ${config.url}`);
        } catch (error) {
          const logger = getLogger();
          logger.error(`❌ [External API] Auth setup failed: ${String(error)}`);
        }

        return config;
      },
      (error) => {
        const logger = getLogger();
        logger.error(`❌ [API-CLIENT] Request interceptor failed: ${String(error)}`);
        return Promise.reject(error);
      },
    );

    // Enhanced response interceptor to handle auth failures and token mismatches
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Handle 401/403 responses that might indicate token mismatch
        if (
          (error.response?.status === 401 || error.response?.status === 403) &&
          !originalRequest._retried
        ) {
          const logger = getLogger();
          logger.warn(
            `⚠️ [API-CLIENT] ${error.response.status} response detected, checking for token mismatch`,
          );

          originalRequest._retried = true;

          try {
            const sessionId = await getCurrentSessionId();
            if (sessionId) {
              // Import functions dynamically to avoid circular imports
              const { validateSessionConsistency, clearSession } = await import('@/lib/session');
              const validation = await validateSessionConsistency(sessionId);

              if (validation.hasMismatch) {
                logger.error(
                  `❌ [API-CLIENT] Token mismatch confirmed after ${error.response.status} response`,
                );

                if (validation.canRecover) {
                  logger.info(`🔄 [API-CLIENT] Attempting to recover from token mismatch`);
                  // The validateSessionConsistency function should have attempted recovery
                  // Retry the original request once
                  return this.client.request(originalRequest);
                } else {
                  logger.error(`❌ [API-CLIENT] Unrecoverable token mismatch, clearing session`);
                  await clearSession(sessionId);
                }
              }
            }
          } catch (validationError) {
            logger.error(
              `❌ [API-CLIENT] Error during token mismatch validation: ${String(validationError)}`,
            );
          }
        }

        return Promise.reject(error);
      },
    );
  }

  // Generic methods for different HTTP verbs
  async get(url: string, params?: Record<string, unknown>) {
    return this.client.get(url, { params });
  }

  async post(url: string, data?: Record<string, unknown>) {
    return this.client.post(url, data);
  }

  async put(url: string, data?: Record<string, unknown>) {
    return this.client.put(url, data);
  }

  async patch(url: string, data?: Record<string, unknown>) {
    return this.client.patch(url, data);
  }

  async delete(url: string) {
    return this.client.delete(url);
  }
}

// Export singleton instance
export const externalClient = new ExternalAPIClient();
export default externalClient;

/**
 * Helper function to get auth headers following the auth wall guide
 * Priority: ID Token → Access Token → API Key
 */
export async function getAuthHeaders(): Promise<Record<string, string>> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  try {
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);
    const userEmail = await getUserEmailFromToken();

    // Priority: ID Token → Access Token → API Key (following auth wall guide)
    if (tokens.idToken) {
      // Option 1: ID Token (Recommended)
      headers['id-token'] = tokens.idToken;
      if (userEmail) {
        headers['email'] = userEmail; // optional with ID token
      }
      headers['domain'] = 'qbraid'; // optional
    } else if (tokens.accessToken) {
      // Option 2: Access Token (Fallback)
      headers['access-token'] = tokens.accessToken;
      if (userEmail) {
        headers['email'] = userEmail; // REQUIRED with access token
      }
      headers['domain'] = 'qbraid'; // optional
    } else if (process.env.QBRAID_API_TOKEN) {
      // Option 3: API Key (Server-to-server fallback)
      headers['api-key'] = process.env.QBRAID_API_TOKEN;
      headers['domain'] = 'qbraid'; // optional
    }

    // Always try to include email for better security
    if (userEmail) {
      headers['email'] = userEmail;
    } else if (process.env.QBRAID_API_EMAIL) {
      headers['email'] = process.env.QBRAID_API_EMAIL;
    }

    return headers;
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [AUTH-HEADERS] Failed to get auth headers: ${String(error)}`);
    return headers; // Return basic headers if auth setup fails
  }
}

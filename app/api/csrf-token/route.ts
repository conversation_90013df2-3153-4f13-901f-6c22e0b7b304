import { NextRequest, NextResponse } from 'next/server';
import { generateCSRFToken } from '../../../lib/session';

/**
 * GET endpoint to generate and return a CSRF token
 * Used by client-side components to get CSRF tokens for form protection
 */
export async function GET() {
  try {
    // Generate a new CSRF token
    const csrfToken = await generateCSRFToken();

    return NextResponse.json(
      {
        csrfToken,
        success: true,
      },
      {
        status: 200,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          Pragma: 'no-cache',
        },
      },
    );
  } catch (error) {
    console.error('CSRF token generation error:', error);

    return NextResponse.json(
      {
        error: 'Failed to generate CSRF token',
        success: false,
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          Pragma: 'no-cache',
        },
      },
    );
  }
}

// Disable other HTTP methods
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

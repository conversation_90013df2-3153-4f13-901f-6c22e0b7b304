import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient, isRedisHealthy } from '@/lib/redis';
import { validateSessionConsistency } from '@/lib/session';
import { getLogger } from '@/lib/logger';

/**
 * Debug endpoint to detect and report token mismatches across all sessions
 * GET /api/debug/token-mismatch
 *
 * Query parameters:
 * - fix=true: Attempt to fix recoverable mismatches
 * - detail=true: Include detailed session information
 */
export async function GET(request: NextRequest) {
  // Security: Only allow in development or with admin token
  if (process.env.NODE_ENV === 'production' && !request.headers.get('x-admin-token')) {
    return NextResponse.json(
      { error: 'Debug endpoints require admin token in production' },
      { status: 403 },
    );
  }

  const startTime = Date.now();
  const logger = getLogger();
  const searchParams = request.nextUrl.searchParams;
  const shouldFix = searchParams.get('fix') === 'true';
  const includeDetail = searchParams.get('detail') === 'true';

  logger.info('🔍 [DEBUG] Starting token mismatch analysis...');

  try {
    const result = {
      timestamp: new Date().toISOString(),
      redis: {
        available: false,
        healthy: false,
      },
      analysis: {
        totalSessions: 0,
        validSessions: 0,
        expiredSessions: 0,
        mismatchedSessions: 0,
        recoverableMismatches: 0,
        unrecoverableMismatches: 0,
        fixedMismatches: 0,
      },
      performance: {
        analysisTime: 0,
        fixTime: 0,
      },
      details: [] as any[],
      errors: [] as string[],
      recommendations: [] as string[],
    };

    // Check Redis availability
    try {
      result.redis.healthy = await isRedisHealthy();
      result.redis.available = true;
      logger.info('✅ [DEBUG] Redis is available and healthy');
    } catch (error) {
      result.errors.push(`Redis health check failed: ${error}`);
      logger.warn('⚠️ [DEBUG] Redis not available');
      return NextResponse.json(result, { status: 500 });
    }

    const redis = getRedisClient();

    // Get all session keys
    const sessionKeys = await redis.keys('session:*');
    result.analysis.totalSessions = sessionKeys.length;

    logger.info(`🔍 [DEBUG] Found ${sessionKeys.length} sessions to analyze`);

    if (sessionKeys.length === 0) {
      logger.info('✨ [DEBUG] No sessions found in Redis');
      return NextResponse.json(result);
    }

    const analysisStart = Date.now();

    // Analyze each session
    for (const sessionKey of sessionKeys) {
      try {
        const sessionId = sessionKey.replace('session:', '');
        const sessionDetail = {
          sessionId: sessionId.substring(0, 10) + '...',
          fullSessionId: includeDetail ? sessionId : undefined,
          status: 'unknown',
          email: 'unknown',
          hasTokens: false,
          canRecover: false,
          errors: [] as string[],
          actions: [] as string[],
        };

        // Get session data
        const sessionData = await redis.get(sessionKey);
        if (!sessionData) {
          sessionDetail.status = 'missing';
          sessionDetail.errors.push('Session data not found');
          result.details.push(sessionDetail);
          continue;
        }

        const session = JSON.parse(sessionData);
        sessionDetail.email = session.email || 'unknown';

        // Check if session is expired
        if (session.exp && Date.now() / 1000 > session.exp) {
          result.analysis.expiredSessions++;
          sessionDetail.status = 'expired';
          sessionDetail.errors.push('Session expired');

          if (shouldFix) {
            await redis.del(sessionKey);
            await redis.del(`cognito:${sessionId}`);
            sessionDetail.actions.push('Cleaned up expired session');
            result.analysis.fixedMismatches++;
          }

          result.details.push(sessionDetail);
          continue;
        }

        // Validate session consistency
        const validation = await validateSessionConsistency(sessionId);

        if (validation.isValid && !validation.hasMismatch) {
          result.analysis.validSessions++;
          sessionDetail.status = 'valid';
          sessionDetail.hasTokens = true;
        } else if (validation.hasMismatch) {
          result.analysis.mismatchedSessions++;
          sessionDetail.status = 'mismatch';
          sessionDetail.hasTokens = false;
          sessionDetail.canRecover = validation.canRecover;
          sessionDetail.errors.push(...validation.errors);

          if (validation.canRecover) {
            result.analysis.recoverableMismatches++;
            if (shouldFix) {
              sessionDetail.actions.push('Attempted token recovery');
              // The validateSessionConsistency function should have attempted recovery
            }
          } else {
            result.analysis.unrecoverableMismatches++;
            if (shouldFix) {
              await redis.del(sessionKey);
              await redis.del(`cognito:${sessionId}`);
              sessionDetail.actions.push('Cleared unrecoverable session');
              result.analysis.fixedMismatches++;
            }
          }
        } else {
          sessionDetail.status = 'invalid';
          sessionDetail.errors.push(...validation.errors);
        }

        if (includeDetail || sessionDetail.status !== 'valid') {
          result.details.push(sessionDetail);
        }
      } catch (error) {
        result.errors.push(`Error analyzing session ${sessionKey}: ${error}`);
        logger.error(`❌ [DEBUG] Error analyzing session ${sessionKey}: ${String(error)}`);
      }
    }

    result.performance.analysisTime = Date.now() - analysisStart;

    // Generate recommendations
    if (result.analysis.mismatchedSessions > 0) {
      result.recommendations.push(
        `Found ${result.analysis.mismatchedSessions} sessions with token mismatches`,
      );

      if (result.analysis.recoverableMismatches > 0) {
        result.recommendations.push(
          `${result.analysis.recoverableMismatches} mismatches may be recoverable from cookies`,
        );
      }

      if (result.analysis.unrecoverableMismatches > 0) {
        result.recommendations.push(
          `${result.analysis.unrecoverableMismatches} mismatches require user re-authentication`,
        );
      }

      result.recommendations.push(
        'Consider investigating why tokens are missing - check Redis TTL settings and cleanup processes',
      );
    }

    if (result.analysis.expiredSessions > 0) {
      result.recommendations.push(
        `${result.analysis.expiredSessions} expired sessions should be cleaned up`,
      );
    }

    const consistencyRatio =
      result.analysis.totalSessions > 0
        ? ((result.analysis.validSessions / result.analysis.totalSessions) * 100).toFixed(1)
        : '100.0';

    result.recommendations.push(
      `Session consistency ratio: ${consistencyRatio}% (${result.analysis.validSessions}/${result.analysis.totalSessions})`,
    );

    result.performance.analysisTime = Date.now() - startTime;

    logger.info(
      `✅ [DEBUG] Token mismatch analysis completed: ${result.analysis.totalSessions} sessions, ${result.analysis.mismatchedSessions} mismatches, ${result.analysis.fixedMismatches} fixed, ${result.performance.analysisTime}ms`,
    );

    return NextResponse.json(result);
  } catch (error) {
    logger.error(`❌ [DEBUG] Token mismatch analysis failed: ${String(error)}`);
    return NextResponse.json(
      {
        error: 'Token mismatch analysis failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

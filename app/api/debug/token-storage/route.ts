import { NextRequest, NextResponse } from 'next/server';
import { RedisTokenStorage } from '@/lib/redis-token-storage';
import { getLogger } from '@/lib/logger';

/**
 * Token storage debug endpoint
 * GET /api/debug/token-storage
 *
 * Returns storage statistics for monitoring
 * Only available in development environment
 */
export async function GET(request: NextRequest) {
  // Security: Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Debug endpoints not available in production' },
      { status: 403 },
    );
  }

  try {
    // Create a temporary storage instance to get stats
    const tokenStorage = new RedisTokenStorage();
    const stats = await tokenStorage.getStats();

    const debugData = {
      timestamp: new Date().toISOString(),
      tokenStorage: {
        ...stats,
        type: stats.redisAvailable ? 'redis' : 'memory',
      },
      environment: {
        redisUrl: process.env.NEXT_PUBLIC_REDIS_URL || 'not configured',
        nodeEnv: process.env.NODE_ENV,
      },
    };

    return NextResponse.json(debugData, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    const logger = getLogger();
    logger.error('❌ [DEBUG] Token storage debug error:', error);

    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
        tokenStorage: {
          redisAvailable: false,
          memoryTokens: 0,
          type: 'error',
        },
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Content-Type': 'application/json',
        },
      },
    );
  }
}

/**
 * Clear token storage (development only)
 * DELETE /api/debug/token-storage
 */
export async function DELETE(request: NextRequest) {
  // Security: Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Debug endpoints not available in production' },
      { status: 403 },
    );
  }

  try {
    const tokenStorage = new RedisTokenStorage();
    await tokenStorage.clear();

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      message: 'Token storage cleared successfully',
    });
  } catch (error) {
    const logger = getLogger();
    logger.error('❌ [DEBUG] Failed to clear token storage:', error);

    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Al<PERSON><PERSON>riangle, ArrowLeft, Building2 } from 'lucide-react';
import Link from 'next/link';

// Simple unauthorized page that doesn't depend on dashboard context
export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto h-12 w-12 text-red-400">
              <AlertTriangle className="w-12 h-12" />
            </div>
            <CardTitle className="text-red-600">Access Denied</CardTitle>
            <CardDescription>
              You don't have sufficient permissions to access the requested resource
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Information about access */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Access Information
              </h4>
              <div className="space-y-2">
                <p className="text-sm text-blue-800">
                  You've attempted to access a resource that requires specific permissions or
                  organization access.
                </p>
                <p className="text-sm text-blue-700">This could be due to:</p>
                <ul className="text-sm text-blue-700 list-disc list-inside ml-2 space-y-1">
                  <li>Insufficient role permissions</li>
                  <li>Not being a member of the required organization</li>
                  <li>Accessing a resource outside your organization scope</li>
                  <li>Session expiration or authentication issues</li>
                </ul>
              </div>
            </div>

            {/* Actions */}
            <div className="pt-4 border-t">
              <p className="text-sm text-gray-600 mb-4">
                If you believe you should have access to this resource, please contact your
                organization administrator or try signing in again.
              </p>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button asChild className="flex-1">
                  <Link href="/" className="flex items-center gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    Go to Dashboard
                  </Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/signin">Sign In</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

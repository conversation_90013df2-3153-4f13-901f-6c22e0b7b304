'use client';

import { DeviceOverviewCard } from '@/components/device-overview-card';
import { useAllDevices } from '@/hooks/use-api';
import { Loader2 } from 'lucide-react';

/**
 * DashboardDevicesOverview
 * ------------------------
 * Lightweight grid shown on the main dashboard page that lists up to 4 devices
 * using the compact DeviceOverviewCard component. Serves as a quick glance for
 * the user; clicking "View" on a card navigates to the Devices page.
 */
export function DashboardDevicesOverview({ limit = 4 }: { limit?: number }) {
  const { data: devices, isLoading } = useAllDevices({ provider: 'AWS' });

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="size-6 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!devices || devices.length === 0) return null;

  const displayDevices = devices.slice(0, limit);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 px-4 lg:px-6">
      {displayDevices.map((device) => (
        <DeviceOverviewCard key={device.qbraid_id} {...device} />
      ))}
    </div>
  );
}

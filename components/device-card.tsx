/**
 * DeviceCard component
 * --------------------
 * This module defines the DeviceCard React component, which displays detailed information
 * about a quantum device. It includes tabs for device details, jobs info, qubit topology,
 * and calibration metrics. The jobs info tab fetches and displays job data using DeviceJobsTab.
 * The component is styled for the qBraid partner dashboard and expects device information as props.
 */

'use client';
import { useState, type ReactNode } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Download,
  Edit,
  Cpu,
  DollarSign,
  Building,
  Package,
  MoreVertical,
  ChevronDown,
  ChevronUp,
  type LucideIcon,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator as DropdownSeparator,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { QubitTopology } from '@/components/qubit-topology';
import { CalibrationMetrics } from '@/components/calibration-metrics';
import { DeviceJobsTab } from '@/components/device-jobs-tab';
import Link from 'next/link';
import type { DeviceCardProps } from '@/types/device';

// Utility component: Column for metric display
type MetricColumnProps = {
  icon: LucideIcon;
  label: string;
  value: ReactNode;
};

function MetricColumn({ icon: Icon, label, value }: MetricColumnProps) {
  return (
    <div className="flex flex-col items-center text-center">
      <Icon className="w-5 h-5 mb-1 text-[#8a2be2]" />
      <span className="text-[#94a3b8] text-xs sm:text-sm font-medium">{label}</span>
      <span className="text-white text-lg sm:text-xl font-semibold break-words">{value}</span>
    </div>
  );
}

// Utility component: Row for label-value pairs in cards
type InfoRowProps = {
  label: string;
  value: ReactNode;
  className?: string;
  children?: ReactNode;
};

function InfoRow({ label, value, className, children }: InfoRowProps) {
  return (
    <div
      className={`flex items-center justify-between py-2 first:pt-0 last:pb-0 ${className ?? ''}`}
    >
      <span className="text-[#94a3b8] text-xs sm:text-sm">{label}</span>
      <span className="text-white text-xs sm:text-sm font-medium text-right break-words max-w-[60%]">
        {value}
      </span>
      {children}
    </div>
  );
}

export function DeviceCard({
  qbraid_id,
  name,
  type,
  deviceDescription,
  paradigm,
  numberQubits,
  status,
  provider,
  architecture = 'N/A',
  processorType = 'N/A',
  pricing,
  vendor,
  runPackage,
  pendingJobs,
  noiseModels,
  defaultTab = 'details',
}: DeviceCardProps) {
  // Calibration metrics data
  const [isOpen, setIsOpen] = useState(false);
  const calibrationMetrics = [
    {
      name: 'Readout Assignment Error',
      median: '1.587e-2',
      min: '6.615e-3',
      max: '2.222e-1',
      color: '#3b82f6',
      value: 40,
    },
    {
      name: 'ECR Error',
      median: '6.560e-3',
      min: '3.206e-3',
      max: '2.481e-1',
      color: '#3b82f6',
      value: 30,
    },
    {
      name: 'SX Error',
      median: '2.219e-4',
      color: '#3b82f6',
      value: 75,
    },
    {
      name: 'T1 Relaxation Time',
      median: '280.82 μs',
      color: '#8b5cf6',
      value: 60,
    },
    {
      name: 'T2 Coherence Time',
      median: '214.87 μs',
      color: '#8b5cf6',
      value: 50,
    },
  ];

  const statusColor = (() => {
    if (!status) return '#94a3b8'; // gray for undefined or null status (retired)
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
        return '#22c55e'; // green
      case 'offline':
        return '#ef4444'; // red
      default:
        return '#94a3b8'; // gray
    }
  })();

  // how the card will display
  return (
    <Card className="w-full bg-gradient-to-t from-sidebar to-sidebar/80 border border-sidebar-border text-white rounded-xl">
      {/* The header of the quantum device card */}
      <CardHeader
        className="pb-4 sm:pb-6 px-4 sm:px-6 cursor-pointer"
        onClick={() => setIsOpen((prev) => !prev)}
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 sm:gap-4">
            <div className="flex-1 min-w-0">
              <h2 className="text-xl sm:text-2xl font-semibold text-white break-words">{name}</h2>
              <p className="text-[#94a3b8] text-sm sm:text-base leading-relaxed mb-2 break-words">
                {deviceDescription}
              </p>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                <Badge className="bg-[#8a2be2]/90 text-white hover:bg-[#8a2be2] border-0 font-medium px-3 py-1 w-fit transition-all duration-200 hover:scale-105">
                  {type}
                </Badge>
                <div className="flex items-center gap-2">
                  <div
                    className="w-2 h-2 rounded-full flex-shrink-0 animate-pulse"
                    style={{ backgroundColor: statusColor }}
                  ></div>
                  <span className="text-[#94a3b8] text-sm font-medium">{status}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2 sm:gap-3 flex-shrink-0 items-start">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="border-0 text-[#94a3b8] hover:text-white h-9 w-9 bg-transparent"
                    aria-label="Actions"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-sidebar border-sidebar-border text-white">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onSelect={(e) => {
                      e.preventDefault();
                      // Trigger export handler (future implementation)
                    }}
                  >
                    <Download className="mr-2 h-4 w-4" /> Export
                  </DropdownMenuItem>
                  <DropdownSeparator className="bg-sidebar-border" />
                  <DropdownMenuItem asChild>
                    <Link href={`/edit-device?id=${qbraid_id}`} className="flex items-center">
                      <Edit className="mr-2 h-4 w-4" /> Edit Info
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {/* Expand / Collapse chevron */}
              <Button
                size="icon"
                variant="ghost"
                className="text-[#94a3b8] hover:text-white h-8 w-8"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen((prev) => !prev);
                }}
                aria-label={isOpen ? 'Collapse' : 'Expand'}
              >
                {isOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>

      {isOpen && (
        <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
          {/* The tab bar on the quantum device card */}
          <Tabs defaultValue={defaultTab} className="w-full">
            <div className="overflow-x-auto -mx-4 sm:mx-0 mb-6 sm:mb-8 w-fit">
              <TabsList className="bg-sidebar border-sidebar-border rounded-lg p-1 w-max sm:w-full justify-start h-auto mx-4 sm:mx-0 transition-all duration-200">
                <TabsTrigger
                  value="details"
                  className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white text-[#94a3b8] rounded-md px-3 sm:px-4 py-2 mr-1 font-medium transition-all duration-200 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/10"
                >
                  Details
                </TabsTrigger>
                <TabsTrigger
                  value="jobs-info"
                  className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white text-[#94a3b8] rounded-md px-3 sm:px-4 py-2 mr-1 font-medium transition-all duration-200 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/10"
                >
                  Jobs
                </TabsTrigger>
                <TabsTrigger
                  value="qubit-topology"
                  className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white text-[#94a3b8] rounded-md px-3 sm:px-4 py-2 mr-1 font-medium transition-all duration-200 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/10"
                >
                  Topology
                </TabsTrigger>
                <TabsTrigger
                  value="calibration"
                  className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white text-[#94a3b8] rounded-md px-3 sm:px-4 py-2 font-medium transition-all duration-200 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/10"
                >
                  Calibration
                </TabsTrigger>
              </TabsList>
            </div>

            {/* The Device Details tab on the quantum device card */}
            <TabsContent value="details" className="mt-0">
              <div className="space-y-6 sm:space-y-8">
                {/* Key Metrics */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 border-b border-sidebar-border pb-6">
                  <MetricColumn icon={Cpu} label="Qubits" value={numberQubits} />
                  <MetricColumn
                    icon={DollarSign}
                    label="Cost/Min"
                    value={
                      pricing?.perMinute !== null && pricing?.perMinute !== undefined
                        ? `$${pricing.perMinute}`
                        : 'Free'
                    }
                  />
                  <MetricColumn icon={Building} label="Provider" value={provider} />
                </div>

                {/* Detailed Information */}
                <div className="grid lg:grid-cols-2 gap-6">
                  {/* System Information */}
                  <Card className="bg-sidebar border-sidebar-border">
                    <CardHeader className="pb-4">
                      <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                        <Package className="w-5 h-5 text-[#8a2be2]" />
                        System Information
                      </h3>
                    </CardHeader>
                    <CardContent className="space-y-0">
                      <InfoRow label="Paradigm" value={paradigm} />
                      <Separator className="my-1 bg-sidebar-border" />
                      <InfoRow label="Vendor" value={vendor} />
                      <Separator className="my-1 bg-sidebar-border" />
                      <InfoRow
                        label={type === 'Simulator' ? 'Processor Type' : 'Architecture'}
                        value={
                          type === 'Simulator' ? processorType || 'N/A' : architecture || 'N/A'
                        }
                      />
                      <Separator className="my-1 bg-sidebar-border" />
                      <InfoRow label="Run Package" value={runPackage} />
                      <Separator className="my-1 bg-sidebar-border" />
                      <InfoRow
                        label="Noise Model"
                        value={
                          noiseModels && noiseModels.length > 0 ? noiseModels.join(', ') : 'N/A'
                        }
                      />
                    </CardContent>
                  </Card>

                  {/* Pricing & Operations */}
                  <Card className="bg-sidebar border-sidebar-border">
                    <CardHeader className="pb-4">
                      <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                        <DollarSign className="w-5 h-5 text-[#8a2be2]" />
                        Pricing & Operations
                      </h3>
                    </CardHeader>
                    <CardContent className="space-y-0">
                      <InfoRow
                        label="Cost per Shot"
                        value={
                          pricing?.perShot !== null && pricing?.perShot !== undefined
                            ? `$${pricing.perShot}`
                            : 'Free'
                        }
                      />
                      <Separator className="my-1 bg-sidebar-border" />
                      <InfoRow
                        label="Cost per Task"
                        value={
                          pricing?.perTask !== null && pricing?.perTask !== undefined
                            ? `$${pricing.perTask}`
                            : 'Free'
                        }
                      />
                      <Separator className="my-1 bg-sidebar-border" />
                      <InfoRow label="Pending Jobs" value={pendingJobs ?? 'N/A'} />
                      <Separator className="my-1 bg-sidebar-border" />
                      <InfoRow label="Queue Status" value={status} />
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            {/* The Jobs Info tab on the quantum device card */}
            <TabsContent value="jobs-info" className="mt-0">
              <DeviceJobsTab></DeviceJobsTab>
            </TabsContent>

            {/* The Qubit Topology tab on the quantum device card */}
            <TabsContent value="qubit-topology" className="mt-0">
              <QubitTopology
                qubits={parseInt(numberQubits, 10)}
                error2QBest="3.36e-3"
                error2QLayered="3.90e-2"
                clops="249K"
              />
            </TabsContent>

            {/* The Calibration tab on the quantum device card */}
            <TabsContent value="calibration" className="mt-0">
              <CalibrationMetrics metrics={calibrationMetrics} />
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  );
}

/*
  DeviceOverviewCard
  ------------------
  Compact card to display a brief overview of a quantum device.
  - Shows device name, provider, status, basic specs
  - "View" button opens a modal with full DeviceCard details (fits a fixed size modal)
  - "Edit" button links to edit page for the device
  - Three-dot menu contains Edit, Hide and Delete actions
*/
'use client';

import Link from 'next/link';
import { Card, CardContent, CardHeader, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  MoreVerticalIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  EyeOffIcon,
  WifiIcon,
  WifiOffIcon,
  MonitorIcon,
  ServerIcon,
  CpuIcon,
} from 'lucide-react';
import type { DeviceCardProps } from '@/types/device';

export function DeviceOverviewCard(props: DeviceCardProps) {
  const { qbraid_id, name, type, status, provider, numberQubits, architecture, pricing } = props;

  // Map status to colour for indicator badge
  const statusColour = (() => {
    if (!status) return '#94a3b8';
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
        return '#22c55e';
      case 'offline':
        return '#ef4444';
      default:
        return '#94a3b8';
    }
  })();

  // Generates a short subtitle for the device type / architecture
  const subtitle = [type, architecture].filter(Boolean).join(' • ');

  const isOnline = status && ['online', 'active'].includes(status.toLowerCase());
  const statusStyles = isOnline
    ? 'bg-emerald-800/30 text-emerald-400'
    : 'bg-muted text-muted-foreground';
  const StatusIcon = isOnline ? WifiIcon : WifiOffIcon;

  return (
    <Card className="bg-gradient-to-t from-sidebar to-sidebar/80 border border-sidebar-border hover:border-[#4b5563] hover:shadow-lg transition-all duration-300 group/card rounded-xl flex flex-col">
      <CardHeader className="pb-2 flex-row items-start pt-4 relative">
        <div className="flex items-center w-full">
          {/* Device Icon */}
          <div
            className="w-9 h-9 rounded-lg flex items-center justify-center flex-shrink-0 mr-2"
            style={{ backgroundColor: `${statusColour}1A` }}
          >
            {(() => {
              const lower = (type || '').toLowerCase();
              const IconCmp = lower.includes('qpu')
                ? ServerIcon
                : lower.includes('sim')
                  ? CpuIcon
                  : MonitorIcon;
              return <IconCmp className="size-4" style={{ color: statusColour }} />;
            })()}
          </div>
          {/* Name + subtitle */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-white truncate leading-none mb-1">{name}</h3>
            <p className="text-xs text-muted-foreground truncate capitalize leading-none">
              {subtitle}
            </p>
          </div>
          {/* Action menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="icon"
                variant="ghost"
                className="justify-end ml-auto text-muted-foreground hover:text-white hover:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 focus:bg-transparent focus:ring-0 flex-shrink-0 p-0"
                aria-label="Open menu"
              >
                <MoreVerticalIcon className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              side="bottom"
              align="end"
              sideOffset={4}
              className="bg-sidebar border-sidebar-border min-w-40"
            >
              <Link href={`/edit-device?id=${qbraid_id}`} passHref>
                <DropdownMenuItem>
                  <PencilIcon className="size-4" /> Edit
                </DropdownMenuItem>
              </Link>
              <DropdownMenuItem disabled>
                <EyeOffIcon className="size-4" /> Hide
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled className="text-destructive focus:text-destructive">
                <TrashIcon className="size-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="flex-1 space-y-3 pb-4">
        {/* Status Bubble */}
        <div className="flex items-center justify-between">
          <div
            className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium`}
            style={{ backgroundColor: `${statusColour}1A` }}
          >
            <StatusIcon className="size-3.5" style={{ color: statusColour }} />
            {status?.toLowerCase() || 'unknown'}
          </div>
          <span className="text-xs text-muted-foreground">
            {isOnline ? 'Active now' : '1 hour ago'}
          </span>
        </div>

        {/* Details list */}
        <div className="space-y-2">
          <div className="flex justify-between gap-4">
            <p className="text-[0.7rem] text-muted-foreground uppercase">Provider:</p>
            <p className="text-xs text-white truncate">{provider}</p>
          </div>
          <div className="flex justify-between gap-4">
            <p className="text-[0.7rem] text-muted-foreground uppercase">Qubits:</p>
            <p className="text-xs text-white">{numberQubits}</p>
          </div>
          <div className="flex justify-between gap-4">
            <p className="text-[0.7rem] text-muted-foreground uppercase">Jobs:</p>
            <p className="text-xs text-white">3{/* {pendingJobs} */}</p>
          </div>
          <div className="flex justify-between gap-4">
            <p className="text-[0.7rem] text-muted-foreground uppercase">Pricing:</p>
            <p className="text-xs text-white">${pricing.perMinute}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-0 pb-4 mt-auto">
        {/* View button now navigates to the devices page focusing on this device */}
        <Link href={`/devices?id=${qbraid_id}`} passHref>
          <Button
            size="sm"
            variant="outline"
            className="w-full border-sidebar-border bg-sidebar text-white hover:bg-sidebar/60 hover:border-[#4b5563]"
          >
            <EyeIcon className="size-4 mr-1" /> View
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

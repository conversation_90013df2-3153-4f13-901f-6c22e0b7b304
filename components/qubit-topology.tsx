'use client';

import { useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface QubitTopologyProps {
  qubits: number;
  error2QBest: string;
  error2QLayered: string;
  clops: string;
}

export function QubitTopology({ qubits, error2QBest, error2QLayered, clops }: QubitTopologyProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  // Store hexagon metadata for hit detection
  interface HexMeta {
    x: number;
    y: number;
    radius: number;
    index: number;
    baseColor: string;
  }

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    if (!ctx) return;

    // Set canvas dimensions
    canvas!.width = canvas!.offsetWidth;
    canvas!.height = canvas!.offsetHeight;

    // Define hexagon grid parameters (larger radius for bigger hexagons)
    const hexRadius = 28;
    const hexHeight = hexRadius * Math.sqrt(3);
    const hexWidth = hexRadius * 2;
    const horizontalSpacing = hexWidth * 0.9;
    const verticalSpacing = hexHeight * 0.75;

    // Define colors for different qubit states
    const colors = {
      normal: '#3b82f6', // blue
      error: '#ef4444', // red
      warning: '#f59e0b', // amber
      selected: '#8b5cf6', // purple
      hover: '#ffffff', // border color on hover
    };

    // Special qubits legend
    const specialQubits: Record<number, keyof typeof colors> = {
      1: 'warning',
      15: 'error',
      27: 'warning',
      32: 'error',
      52: 'warning',
      76: 'error',
      89: 'error',
      94: 'warning',
      118: 'warning',
    };

    // Calculate grid dimensions based on number of qubits
    const gridWidth = 16;
    const gridHeight = Math.ceil(qubits / gridWidth);

    // Calculate starting offsets to center the grid within the canvas
    const contentWidth = (gridWidth - 1) * horizontalSpacing + hexWidth;
    const contentHeight = (gridHeight - 1) * verticalSpacing + hexHeight;
    const startX = (canvas!.width - contentWidth) / 2 + hexRadius;
    const startY = (canvas!.height - contentHeight) / 2 + hexRadius;

    // Store hex metadata
    const hexes: HexMeta[] = [];

    // Function to draw a single hexagon (optionally highlighted)
    function renderHex(hex: HexMeta, isHover: boolean = false) {
      const { x, y, radius, baseColor, index } = hex;
      ctx.beginPath();
      for (let i = 0; i < 6; i++) {
        const angle = (Math.PI / 3) * i;
        const hx = x + radius * Math.cos(angle);
        const hy = y + radius * Math.sin(angle);
        if (i === 0) {
          ctx.moveTo(hx, hy);
        } else {
          ctx.lineTo(hx, hy);
        }
      }
      ctx.closePath();

      ctx.fillStyle = baseColor;
      ctx.fill();

      if (isHover) {
        ctx.lineWidth = 2;
        ctx.strokeStyle = colors.hover;
        ctx.stroke();
      }

      // Draw label
      ctx.fillStyle = 'white';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(index.toString(), x, y);
    }

    // Initial population of hex list
    let qubitIndex = 0;
    for (let row = 0; row < gridHeight; row++) {
      const rowOffset = (row % 2) * (horizontalSpacing / 2);
      for (let col = 0; col < gridWidth; col++) {
        if (qubitIndex >= qubits) break;

        const x = col * horizontalSpacing + rowOffset + startX;
        const y = row * verticalSpacing + startY;

        const colorKey = specialQubits[qubitIndex] || 'normal';
        const hex: HexMeta = {
          x,
          y,
          radius: hexRadius,
          index: qubitIndex,
          baseColor: colors[colorKey],
        };
        hexes.push(hex);
        qubitIndex++;
      }
    }

    // Function to clear and redraw entire grid; highlights hovered hex index if provided
    function drawAll(hoverIndex: number | null) {
      ctx.clearRect(0, 0, canvas!.width, canvas!.height);
      hexes.forEach((hex) => {
        renderHex(hex, hoverIndex === hex.index);
      });
    }

    drawAll(null);

    // Point-in-hexagon check using ray casting algorithm
    function isPointInHex(px: number, py: number, hex: HexMeta) {
      const vertices: [number, number][] = [];
      for (let i = 0; i < 6; i++) {
        const angle = (Math.PI / 3) * i;
        vertices.push([hex.x + hex.radius * Math.cos(angle), hex.y + hex.radius * Math.sin(angle)]);
      }
      let inside = false;
      for (let i = 0, j = 5; i < 6; j = i++) {
        const xi = vertices[i][0],
          yi = vertices[i][1];
        const xj = vertices[j][0],
          yj = vertices[j][1];
        const intersect = yi > py !== yj > py && px < ((xj - xi) * (py - yi)) / (yj - yi) + xi;
        if (intersect) inside = !inside;
      }
      return inside;
    }

    let currentHover: number | null = null;

    function handleMouseMove(e: MouseEvent) {
      const rect = canvas!.getBoundingClientRect();
      const px = e.clientX - rect.left;
      const py = e.clientY - rect.top;

      let newHover: number | null = null;
      for (const hex of hexes) {
        if (isPointInHex(px, py, hex)) {
          newHover = hex.index;
          break;
        }
      }

      if (newHover !== currentHover) {
        currentHover = newHover;
        drawAll(currentHover);
      }
    }

    function handleMouseLeave() {
      if (currentHover !== null) {
        currentHover = null;
        drawAll(null);
      }
    }

    canvas!.addEventListener('mousemove', handleMouseMove);
    canvas!.addEventListener('mouseleave', handleMouseLeave);

    // Cleanup
    return () => {
      canvas!.removeEventListener('mousemove', handleMouseMove);
      canvas!.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [qubits]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-4 gap-8">
        <div>
          <p className="text-[#94a3b8] text-sm">Qubits</p>
          <p className="text-white text-2xl font-bold">{qubits}</p>
        </div>
        <div>
          <p className="text-[#94a3b8] text-sm">2Q Error (Best)</p>
          <p className="text-white text-2xl font-bold">{error2QBest}</p>
        </div>
        <div>
          <p className="text-[#94a3b8] text-sm">2Q Error (Layered)</p>
          <p className="text-white text-2xl font-bold">{error2QLayered}</p>
        </div>
        <div>
          <p className="text-[#94a3b8] text-sm">CLOPS</p>
          <p className="text-white text-2xl font-bold">{clops}</p>
        </div>
      </div>

      <Card className="bg-sidebar border-none">
        <CardContent className="px-4 py-12">
          <canvas ref={canvasRef} className="w-full" />
        </CardContent>
      </Card>
    </div>
  );
}

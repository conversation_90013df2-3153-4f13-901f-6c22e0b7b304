# Redis Token Mismatch Detection & Resolution Guide

## Overview

This guide explains how to detect, understand, and resolve Redis token mismatches in the partner dashboard authentication system.

## Problem Description

### What is a Redis Token Mismatch?

A Redis token mismatch occurs when user session data exists in Redis but the corresponding Cognito authentication tokens are missing or invalid. This creates a broken authentication state where:

- ✅ User appears authenticated (valid session cookie)
- ❌ External API calls fail (missing/invalid Cognito tokens)
- ⚠️ User is not automatically logged out

### Architecture Context

The system uses a three-tier authentication storage:

```
Browser Cookie (session ID) → Redis Session (session:{id}) → Redis Tokens (cognito:{id})
                                     ↓                            ↓
                              User data, roles              Access/ID tokens
                              7-day TTL                     1-hour TTL
```

## Common Mismatch Scenarios

### 1. TTL Mismatch

```bash
# Session exists but tokens expired
✅ session:abc123 (valid, 6 days left)
❌ cognito:abc123 (expired, deleted by Redis)
```

### 2. Manual Cleanup

```bash
# <PERSON><PERSON> accidentally deletes only token keys
redis-cli DEL cognito:*
✅ All sessions remain
❌ All tokens gone
```

### 3. Redis Storage Issues

```bash
# Partial Redis failures or restarts
✅ Sessions restored from persistence
❌ Tokens lost due to shorter TTL
```

## Detection Methods

### 1. Automatic Detection (Real-time)

The system automatically detects mismatches during:

- Session verification (`verifySession()`)
- External API calls (`ExternalAPIClient`)
- Permission checks

```typescript
// Enhanced session verification
const session = await verifySession(sessionId);
// → Automatically detects token mismatch
// → Attempts recovery from cookies
// → Clears session if unrecoverable
```

### 2. Debug API Endpoint

Use the debug endpoint to analyze all sessions:

```bash
# Development
GET /api/debug/token-mismatch

# With automatic fixing
GET /api/debug/token-mismatch?fix=true

# With detailed session info
GET /api/debug/token-mismatch?detail=true
```

### 3. Redis Cleanup Script

Run the enhanced cleanup script:

```bash
# Check for mismatches (dry run)
node scripts/cleanup-redis-sessions.js --fix-mismatches --dry-run

# Fix mismatches
node scripts/cleanup-redis-sessions.js --fix-mismatches

# Clean expired sessions and fix mismatches
node scripts/cleanup-redis-sessions.js --all --fix-mismatches
```

## Resolution Strategies

### 1. Automatic Recovery

The system attempts automatic recovery when possible:

```typescript
// If tokens exist in cookies but not Redis
if (cookieTokens.accessToken || cookieTokens.idToken) {
  await setCognitoTokenCookies(cookieTokens, sessionId);
  // ✅ Mismatch resolved
}
```

### 2. Session Invalidation

For unrecoverable mismatches:

```typescript
// Clear corrupted session to force re-authentication
await clearSession(sessionId);
// → User redirected to login
```

### 3. Graceful API Handling

External API calls handle mismatches gracefully:

```typescript
// API interceptor detects 401/403 responses
// → Validates session consistency
// → Attempts recovery or clears session
// → Retries request or fails gracefully
```

## Prevention Measures

### 1. Aligned TTL Settings

Consider aligning session and token TTLs:

```typescript
// Current settings
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days
const COGNITO_TOKEN_TTL = 60 * 60; // 1 hour

// Recommended: Shorter session TTL or token refresh
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 1 day
const COGNITO_TOKEN_TTL = 60 * 60; // 1 hour
```

### 2. Regular Cleanup

Schedule regular cleanup to prevent accumulation:

```bash
# Cron job example (daily at 2 AM)
0 2 * * * cd /app && node scripts/cleanup-redis-sessions.js --fix-mismatches
```

### 3. Monitoring

Monitor session consistency:

```bash
# Check session health
curl /api/debug/token-mismatch
```

## API Reference

### Session Validation

```typescript
import { validateSessionConsistency } from '@/lib/session';

const validation = await validateSessionConsistency(sessionId);
// Returns: { isValid, hasMismatch, canRecover, errors }
```

### Token Mismatch Detection

```typescript
// Automatic detection during session verification
const session = await verifySession(token);
// Returns null if mismatch detected and unrecoverable
```

### Manual Cleanup

```typescript
import { clearSession } from '@/lib/session';

// Clear specific session and tokens
await clearSession(sessionId);
```

## Troubleshooting

### Symptoms

1. **User appears logged in but gets 401/403 errors**
   - Check: Token mismatch likely
   - Action: Run mismatch detection API

2. **External API calls failing consistently**
   - Check: Cognito tokens missing
   - Action: Validate session consistency

3. **Users randomly logged out**
   - Check: Automatic mismatch detection triggered
   - Action: Investigate why tokens are missing

### Debug Commands

```bash
# Check Redis keys for a session
redis-cli KEYS "*abc123*"

# Inspect session data
redis-cli GET session:abc123

# Check token data
redis-cli GET cognito:abc123

# Check TTL
redis-cli TTL session:abc123
redis-cli TTL cognito:abc123
```

### Log Analysis

Look for these log patterns:

```
⚠️ [SESSION-MISMATCH] No Cognito tokens found in Redis
❌ [SESSION] Token mismatch detected, invalidating session
🔄 [SESSION-MISMATCH] Found tokens in cookies, restoring to Redis
```

## Security Considerations

### 1. Token Exposure

- Tokens are stored in secure HTTP-only cookies as fallback
- Redis provides additional isolation and TTL management
- Never log full token values

### 2. Session Hijacking Protection

- Session IDs are cryptographically secure (32-byte hex)
- Automatic invalidation on mismatch detection
- CSRF protection included

### 3. Clean Logout

Enhanced logout ensures complete cleanup:

```typescript
await logout();
// → Validates session consistency
// → Clears all Redis keys
// → Clears cookies
// → Signs out from Cognito
```

## Best Practices

1. **Regular Monitoring**: Check session consistency weekly
2. **Automated Cleanup**: Schedule daily cleanup jobs
3. **Graceful Degradation**: Handle mismatches transparently
4. **User Communication**: Clear error messages for re-authentication
5. **Logging**: Comprehensive logging for debugging

## Implementation Checklist

- [x] Automatic mismatch detection in session verification
- [x] Recovery from cookie fallback
- [x] Graceful API error handling
- [x] Enhanced logout cleanup
- [x] Debug endpoint for analysis
- [x] Cleanup script enhancement
- [x] Comprehensive logging
- [x] Session consistency validation

This implementation ensures robust handling of Redis token mismatches while maintaining a smooth user experience.

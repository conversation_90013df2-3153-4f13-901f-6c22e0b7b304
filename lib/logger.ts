// logger.ts
import { serializeError } from 'serialize-error';

// Simple logger that works in all environments (Node.js, Edge Runtime, browser)
class SimpleLogger {
  private isServer = typeof window === 'undefined';

  private formatMessage(level: string, message: string): string {
    const timestamp = new Date().toISOString();
    const prefix = this.isServer ? `[${timestamp}] ${level.toUpperCase()}` : level.toUpperCase();
    return `${prefix}: ${message}`;
  }

  info(message: string, data?: unknown): void {
    const dataDetails = data ? ` | Data: ${JSON.stringify(data)}` : '';
    const fullMessage = message + dataDetails;

    if (this.isServer && process.env.NODE_ENV === 'development') {
      console.info(this.formatMessage('info', fullMessage));
    } else if (!this.isServer) {
      console.info(this.formatMessage('info', fullMessage));
    }
  }

  warn(message: string, error?: unknown): void {
    const errorDetails = error ? ` | Error: ${serializeError(error)}` : '';
    const fullMessage = message + errorDetails;

    if (this.isServer && process.env.NODE_ENV === 'development') {
      console.warn(this.formatMessage('warn', fullMessage));
    } else if (!this.isServer) {
      console.warn(this.formatMessage('warn', fullMessage));
    }
  }

  error(message: string, error?: unknown): void {
    const errorDetails = error ? ` | Error: ${serializeError(error)}` : '';
    const fullMessage = message + errorDetails;

    if (this.isServer) {
      console.error(this.formatMessage('error', fullMessage));
    } else {
      console.error(this.formatMessage('error', fullMessage));
    }
  }

  debug(message: string): void {
    if (process.env.NODE_ENV === 'development') {
      if (this.isServer) {
        console.debug(this.formatMessage('debug', message));
      } else {
        console.debug(this.formatMessage('debug', message));
      }
    }
  }
}

const logger = new SimpleLogger();

export function getLogger() {
  return logger;
}

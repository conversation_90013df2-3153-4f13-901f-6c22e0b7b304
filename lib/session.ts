// lib/session.ts

'use server';

import { JWTPayload, SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import { randomBytes } from 'crypto';
import {
  SessionPayload,
  CSRFToken,
  UserSessionData,
  CognitoTokens,
  Permission,
} from '@/types/auth';
import { getLogger } from './logger';
// Dynamic Redis imports to avoid edge runtime issues

// Session configuration
const SESSION_SECRET = new TextEncoder().encode(
  process.env.SESSION_SECRET || 'fallback-secret-key-change-in-production',
);
// Use secure cookie names only in production, simple names in development
const SESSION_COOKIE_NAME = process.env.NODE_ENV === 'production' ? '__Secure-session' : 'session';
const CSRF_COOKIE_NAME = process.env.NODE_ENV === 'production' ? '__Host-csrf-token' : 'csrf-token';
const ACCESS_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-access-token' : 'access-token';
const ID_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-id-token' : 'id-token';
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 1 day
const CSRF_TOKEN_LENGTH = 32;

// Redis key prefixes for serverless session storage
const REDIS_SESSION_PREFIX = 'session:';
const REDIS_COGNITO_PREFIX = 'cognito:';
const REDIS_CSRF_PREFIX = 'csrf:';

// Session storage mode: 'redis' for serverless, 'cookies' for traditional
const STORAGE_MODE = process.env.SESSION_STORAGE_MODE || 'redis'; // Default to Redis for serverless

// Session and CSRF interfaces now imported from @/types/auth

/**
 * Secure cookie configuration with all security flags
 */
const getSecureCookieOptions = (maxAge?: number) => ({
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: maxAge || SESSION_DURATION / 1000,
  path: '/',
  ...(process.env.NODE_ENV === 'production' && {
    domain: process.env.COOKIE_DOMAIN, // Set your domain in production
  }),
});

/**
 * Generate a cryptographically secure random token
 */
function generateSecureToken(length: number = CSRF_TOKEN_LENGTH): string {
  return randomBytes(length).toString('hex');
}

/**
 * Check if we're running in edge runtime (middleware)
 */
function isEdgeRuntime(): boolean {
  // Edge runtime doesn't have Node.js APIs like 'process'
  try {
    // Check if we're in edge runtime by looking for Web APIs instead of Node.js APIs
    return (
      typeof process === 'undefined' ||
      (typeof globalThis !== 'undefined' && 'EdgeRuntime' in globalThis)
    );
  } catch {
    return true; // Assume edge runtime if we can't determine
  }
}

/**
 * Check if Redis is available for session storage
 */
async function useRedisStorage(): Promise<boolean> {
  if (STORAGE_MODE === 'cookies') {
    return false;
  }

  // Don't use Redis in edge runtime (middleware)
  if (isEdgeRuntime()) {
    return false;
  }

  try {
    // Dynamic import to avoid loading Redis in edge runtime
    const { isRedisHealthy } = await import('./redis');
    return await isRedisHealthy();
  } catch {
    return false;
  }
}

/**
 * Store data in Redis with TTL
 */
async function setRedisData(key: string, data: any, ttlSeconds: number): Promise<void> {
  try {
    const { getRedisClient } = await import('./redis');
    const redis = getRedisClient();
    const serializedData = JSON.stringify(data);
    await redis.setex(key, ttlSeconds, serializedData);
    const logger = getLogger();
    logger.info(`🔐 [REDIS-SESSION] Data stored: ${key} (TTL: ${ttlSeconds}s)`);
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [REDIS-SESSION] Failed to store data ${key}: ${error}`);
    throw error;
  }
}

/**
 * Get data from Redis
 */
async function getRedisData(key: string): Promise<any | null> {
  try {
    const { getRedisClient } = await import('./redis');
    const redis = getRedisClient();
    const data = await redis.get(key);
    if (!data) {
      return null;
    }
    return JSON.parse(data);
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [REDIS-SESSION] Failed to get data ${key}: ${error}`);
    return null;
  }
}

/**
 * Remove data from Redis
 */
async function deleteRedisData(key: string): Promise<void> {
  try {
    const { getRedisClient } = await import('./redis');
    const redis = getRedisClient();
    await redis.del(key);
    const logger = getLogger();
    logger.info(`🗑️ [REDIS-SESSION] Data removed: ${key}`);
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [REDIS-SESSION] Failed to remove data ${key}: ${error}`);
  }
}

/**
 * Create a session with Redis or JWT storage
 */
export async function createSession(userData: UserSessionData): Promise<string> {
  const now = Date.now();
  const exp = now + SESSION_DURATION;
  const sessionId = generateSecureToken(16); // Unique session ID

  const sessionData: SessionPayload = {
    username: userData.username,
    email: userData.email,
    userId: userData.userId,
    externalRoles: userData.externalRoles || [],
    permissions: userData.permissions || [],
    signedIn: true,
    iat: Math.floor(now / 1000),
    exp: Math.floor(exp / 1000),
    jti: sessionId,
  };

  // Try Redis storage first for serverless compatibility
  const logger = getLogger();
  if (await useRedisStorage()) {
    const redisKey = `${REDIS_SESSION_PREFIX}${sessionId}`;
    const ttlSeconds = Math.floor(SESSION_DURATION / 1000);

    try {
      await setRedisData(redisKey, sessionData, ttlSeconds);
      logger.info(`✅ [SESSION] Session created in Redis: ${sessionId}`);
      return sessionId; // Return session ID for Redis mode
    } catch (error) {
      logger.warn(`⚠️ [SESSION] Redis storage failed, falling back to JWT: ${error}`);
    }
  }

  // Fallback to JWT for traditional cookie-based sessions
  logger.info(`✅ [SESSION] Session created as JWT: ${sessionId}`);
  return await new SignJWT(sessionData as unknown as JWTPayload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(sessionData.exp)
    .setJti(sessionId)
    .sign(SESSION_SECRET);
}

/**
 * Verify session from Redis or JWT token with enhanced token validation
 */
export async function verifySession(token: string): Promise<SessionPayload | null> {
  if (!token) {
    return null;
  }

  // Check if token looks like a Redis session ID (32 chars, hex) vs JWT (has dots)
  const looksLikeSessionId = token.length === 32 && /^[a-f0-9]+$/i.test(token);
  const looksLikeJWT = token.includes('.') && token.length > 100;

  // Check if we can use Redis storage (not in edge runtime and Redis is healthy)
  const canUseRedis = await useRedisStorage();

  // Try Redis storage first if we have Redis and token looks like session ID
  if (looksLikeSessionId && canUseRedis) {
    try {
      const redisKey = `${REDIS_SESSION_PREFIX}${token}`;
      const sessionData = await getRedisData(redisKey);

      if (sessionData) {
        // Check if session is expired
        if (sessionData.exp && Date.now() / 1000 > sessionData.exp) {
          const logger = getLogger();
          logger.warn(`⚠️ [SESSION] Redis session expired: ${token.substring(0, 10)}...`);
          await deleteRedisData(redisKey);
          return null;
        }

        // Enhanced: Check for token mismatch - verify Cognito tokens exist
        const tokenMismatchDetected = await detectTokenMismatch(token);
        if (tokenMismatchDetected) {
          const logger = getLogger();
          logger.warn(
            `❌ [SESSION] Token mismatch detected for session ${token.substring(0, 10)}..., invalidating session`,
          );

          // Clear the corrupted session to force re-authentication
          await clearSession(token);
          return null;
        }

        const logger = getLogger();
        logger.info(`✅ [SESSION] Session verified from Redis: ${token.substring(0, 10)}...`);
        return sessionData as SessionPayload;
      } else {
        const logger = getLogger();
        logger.warn(
          `❌ [SESSION] Redis session not found for ID: ${token.substring(0, 10)}... (session may have been manually cleared)`,
        );
        return null;
      }
    } catch (error) {
      const logger = getLogger();
      logger.warn(`⚠️ [SESSION] Redis verification failed: ${error}`);
      return null;
    }
  }

  // If token looks like session ID but Redis is not available (e.g., in middleware/edge runtime)
  if (looksLikeSessionId && !canUseRedis) {
    const logger = getLogger();
    logger.warn(
      `⚠️ [SESSION] Redis session ID detected but Redis not available (server runtime issue): ${token.substring(0, 10)}...`,
    );
    return null;
  }

  // Try JWT verification if token looks like a JWT
  if (looksLikeJWT) {
    try {
      const { payload } = await jwtVerify(token, SESSION_SECRET);

      // Validate payload structure
      if (
        typeof payload.username === 'string' &&
        typeof payload.email === 'string' &&
        typeof payload.signedIn === 'boolean' &&
        typeof payload.jti === 'string' &&
        Array.isArray(payload.externalRoles) &&
        Array.isArray(payload.permissions) &&
        payload.signedIn === true
      ) {
        const logger = getLogger();
        logger.info(`✅ [SESSION] Session verified from JWT: ${payload.jti}`);
        return payload as unknown as SessionPayload;
      }

      return null;
    } catch (error) {
      const logger = getLogger();
      logger.error(`❌ [SESSION] JWT verification failed: ${error}`);
      return null;
    }
  }

  // If we get here, the token format is unrecognized
  const logger = getLogger();
  logger.warn(
    `⚠️ [SESSION] Unknown session token format: length=${token.length}, hasDotsF=${token.includes('.')}, sample=${token.substring(0, 10)}...`,
  );
  return null;
}

/**
 * Get current session from cookies
 */
export async function getSession(): Promise<SessionPayload | null> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);

    if (!sessionCookie?.value) {
      return null;
    }

    const session = await verifySession(sessionCookie.value);

    // If session verification returns null (Redis data missing), clear the cookie
    if (!session) {
      const logger = getLogger();
      logger.info('🧹 [SESSION] Session invalid, clearing session cookie');
      cookieStore.delete(SESSION_COOKIE_NAME);
      return null;
    }

    return session;
  } catch (error) {
    const logger = getLogger();
    logger.error(`Session retrieval error: ${error}`);
    return null;
  }
}

/**
 * Middleware-compatible session verification that handles edge runtime limitations
 * This function is optimized for middleware use where Redis may not be available
 */
export async function verifySessionForMiddleware(token: string): Promise<SessionPayload | null> {
  if (!token) {
    return null;
  }

  // Check if token looks like a Redis session ID (32 chars, hex) vs JWT (has dots)
  const looksLikeSessionId = token.length === 32 && /^[a-f0-9]+$/i.test(token);
  const looksLikeJWT = token.includes('.') && token.length > 100;

  // In edge runtime (middleware), we can't use Redis, so we need to handle Redis session IDs differently
  if (looksLikeSessionId) {
    // For Redis session IDs in middleware, we'll return a minimal session object
    // The actual permission checking will happen in the server components
    const logger = getLogger();
    logger.info(
      `🔍 [MIDDLEWARE] Redis session ID detected: ${token.substring(0, 10)}... (will verify in server component)`,
    );

    // Return a placeholder session that indicates authentication but requires server-side verification
    return {
      username: 'authenticated-user',
      email: '<EMAIL>',
      externalRoles: [],
      permissions: [],
      signedIn: true,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 86400, // 24 hours from now
      jti: token,
    };
  }

  // Try JWT verification if token looks like a JWT
  if (looksLikeJWT) {
    try {
      const { payload } = await jwtVerify(token, SESSION_SECRET);

      // Validate payload structure
      if (
        typeof payload.username === 'string' &&
        typeof payload.email === 'string' &&
        typeof payload.signedIn === 'boolean' &&
        typeof payload.jti === 'string' &&
        Array.isArray(payload.externalRoles) &&
        Array.isArray(payload.permissions) &&
        payload.signedIn === true
      ) {
        const logger = getLogger();
        logger.info(`✅ [MIDDLEWARE] Session verified from JWT: ${payload.jti}`);
        return payload as unknown as SessionPayload;
      }

      return null;
    } catch (error) {
      const logger = getLogger();
      logger.error(`❌ [MIDDLEWARE] JWT verification failed: ${error}`);
      return null;
    }
  }

  // If we get here, the token format is unrecognized
  const logger = getLogger();
  logger.warn(
    `⚠️ [MIDDLEWARE] Unknown session token format: length=${token.length}, hasDotsF=${token.includes('.')}, sample=${token.substring(0, 10)}...`,
  );
  return null;
}

/**
 * Set session cookie or store session ID for Redis mode
 */
export async function setSessionCookie(sessionToken: string): Promise<void> {
  const cookieStore = await cookies();

  // In Redis mode, we store a lightweight session ID in the cookie
  // In JWT mode, we store the full JWT token
  cookieStore.set(SESSION_COOKIE_NAME, sessionToken, {
    ...getSecureCookieOptions(),
  });

  const logger = getLogger();
  logger.info(`🍪 [SESSION] Session cookie set: ${sessionToken.substring(0, 10)}...`);
}

/**
 * Generate and set CSRF token
 */
export async function generateCSRFToken(): Promise<string> {
  const token = generateSecureToken();
  const exp = Date.now() + 60 * 60 * 1000; // 1 hour expiry

  const csrfData: CSRFToken = { token, exp };
  const cookieStore = await cookies();

  cookieStore.set(CSRF_COOKIE_NAME, JSON.stringify(csrfData), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60, // 1 hour
    path: '/',
  });

  return token;
}

/**
 * Verify CSRF token from request
 */
export async function verifyCSRFToken(providedToken: string): Promise<boolean> {
  try {
    const cookieStore = await cookies();
    const csrfCookie = cookieStore.get(CSRF_COOKIE_NAME);

    if (!csrfCookie?.value || !providedToken) {
      return false;
    }

    const csrfData: CSRFToken = JSON.parse(csrfCookie.value);

    // Check token match and expiry
    if (csrfData.token === providedToken && Date.now() < csrfData.exp) {
      return true;
    }

    return false;
  } catch (error) {
    const logger = getLogger();
    logger.error(`CSRF token verification failed: ${error}`);
    return false;
  }
}

/**
 * Clear all session and security cookies
 */
export async function clearSession(sessionId?: string): Promise<void> {
  const cookieStore = await cookies();
  const logger = getLogger();

  logger.info('🧹 [SESSION] Clearing all session and token data...');

  // Clear Redis data if available
  if (await useRedisStorage()) {
    try {
      if (sessionId) {
        // Clear specific session data
        const sessionKey = `${REDIS_SESSION_PREFIX}${sessionId}`;
        const cognitoKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;

        await Promise.all([deleteRedisData(sessionKey), deleteRedisData(cognitoKey)]);

        logger.info(`🗑️ [SESSION] Redis data cleared for session: ${sessionId}`);
      } else {
        // If no session ID provided, try to get it from cookie and clean up
        logger.warn('⚠️ [SESSION] No session ID provided for Redis cleanup');

        // Try to get session ID from cookie as fallback
        const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);
        if (sessionCookie?.value) {
          const fallbackSessionId = sessionCookie.value;
          const sessionKey = `${REDIS_SESSION_PREFIX}${fallbackSessionId}`;
          const cognitoKey = `${REDIS_COGNITO_PREFIX}${fallbackSessionId}`;

          await Promise.all([deleteRedisData(sessionKey), deleteRedisData(cognitoKey)]);

          logger.info(
            `🗑️ [SESSION] Redis data cleared using fallback session ID: ${fallbackSessionId.substring(0, 10)}...`,
          );
        }
      }
    } catch (error) {
      logger.warn(`⚠️ [SESSION] Failed to clear Redis data: ${error}`);
    }
  }

  // Clear session cookie
  cookieStore.delete(SESSION_COOKIE_NAME);

  // Clear CSRF token
  cookieStore.delete(CSRF_COOKIE_NAME);

  // Clear Cognito token cookies
  cookieStore.delete(ACCESS_TOKEN_COOKIE_NAME);
  cookieStore.delete(ID_TOKEN_COOKIE_NAME);

  // Clear any legacy cookies
  cookieStore.delete('amplify-auth-token');
  cookieStore.delete('temp-signup-password');

  logger.info('🧹 [SESSION] All session data cleared');
}

/**
 * Refresh session if needed (extend expiry)
 */
export async function refreshSession(currentSession: SessionPayload): Promise<string | null> {
  // Refresh if session will expire in next 24 hours
  const timeUntilExpiry = currentSession.exp * 1000 - Date.now();
  const shouldRefresh = timeUntilExpiry < 24 * 60 * 60 * 1000;

  if (shouldRefresh) {
    return await createSession({
      username: currentSession.username,
      email: currentSession.email,
      userId: currentSession.userId,
      externalRoles: currentSession.externalRoles,
      permissions: currentSession.permissions,
    });
  }

  return null;
}

/**
 * Validate session and optionally refresh
 */
export async function validateAndRefreshSession(): Promise<SessionPayload | null> {
  const session = await getSession();

  if (!session) {
    return null;
  }

  // Try to refresh if needed
  const newToken = await refreshSession(session);
  if (newToken) {
    await setSessionCookie(newToken);
    // Return updated session data
    return await verifySession(newToken);
  }

  return session;
}

/**
 * Set Cognito tokens in Redis or cookies based on storage mode
 */
export async function setCognitoTokenCookies(
  tokens: CognitoTokens,
  sessionId?: string,
): Promise<void> {
  // Try Redis storage first for serverless compatibility
  if ((await useRedisStorage()) && sessionId) {
    try {
      const redisKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
      const ttlSeconds = 60 * 60; // 1 hour TTL

      await setRedisData(redisKey, tokens, ttlSeconds);
      const logger = getLogger();
      logger.info(`🔐 [TOKENS] Cognito tokens stored in Redis for session: ${sessionId}`);
      return;
    } catch (error) {
      const logger = getLogger();
      logger.warn(`⚠️ [TOKENS] Redis storage failed, falling back to cookies: ${error}`);
    }
  }

  // Fallback to cookie storage
  const cookieStore = await cookies();
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 60 * 60, // 1 hour for access/id tokens
    path: '/',
  };

  const logger = getLogger();
  logger.info('🍪 [TOKENS] Setting Cognito token cookies...');

  if (tokens.accessToken) {
    cookieStore.set(ACCESS_TOKEN_COOKIE_NAME, tokens.accessToken, cookieOptions);
    logger.info('🍪 [TOKENS] Access token cookie set');
  }

  if (tokens.idToken) {
    cookieStore.set(ID_TOKEN_COOKIE_NAME, tokens.idToken, cookieOptions);
    logger.info('🍪 [TOKENS] ID token cookie set');
  }
}

/**
 * Get Cognito tokens from Redis or cookies based on storage mode
 */
export async function getCognitoTokenCookies(sessionId?: string): Promise<CognitoTokens> {
  // Try Redis storage first if session ID is available
  if ((await useRedisStorage()) && sessionId) {
    try {
      const redisKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
      const tokens = await getRedisData(redisKey);

      if (tokens) {
        const logger = getLogger();
        logger.info(`🔐 [TOKENS] Retrieved Cognito tokens from Redis for session: ${sessionId}`);
        return tokens as CognitoTokens;
      }
    } catch (error) {
      const logger = getLogger();
      logger.warn(`⚠️ [TOKENS] Redis retrieval failed, trying cookies: ${error}`);
    }
  }

  // Fallback to cookie storage
  try {
    const cookieStore = await cookies();

    const accessToken = cookieStore.get(ACCESS_TOKEN_COOKIE_NAME)?.value;
    const idToken = cookieStore.get(ID_TOKEN_COOKIE_NAME)?.value;

    const logger = getLogger();
    logger.info(
      `🍪 [TOKENS] Retrieved Cognito tokens from cookies: hasAccessToken=${!!accessToken}, hasIdToken=${!!idToken}`,
    );

    return {
      accessToken,
      idToken,
    };
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [TOKENS] Failed to get Cognito tokens: ${error}`);
    return {};
  }
}

/**
 * Clear Cognito token cookies
 */
export async function clearCognitoTokenCookies(): Promise<void> {
  const cookieStore = await cookies();
  const logger = getLogger();

  logger.info('🍪 [TOKENS] Clearing Cognito token cookies...');

  cookieStore.delete(ACCESS_TOKEN_COOKIE_NAME);
  cookieStore.delete(ID_TOKEN_COOKIE_NAME);

  logger.info('🍪 [TOKENS] Cognito token cookies cleared');
}

/**
 * Get current session ID from cookie
 */
export async function getCurrentSessionId(): Promise<string | null> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);
    return sessionCookie?.value || null;
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [SESSION] Failed to get session ID: ${error}`);
    return null;
  }
}

/**
 * Get user email from ID token with session-aware retrieval
 */
export async function getUserEmailFromToken(): Promise<string | null> {
  try {
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (tokens.idToken) {
      // Decode JWT payload (note: this is not secure validation, just for extracting email)
      const payload = JSON.parse(atob(tokens.idToken.split('.')[1]));
      return payload.email || null;
    }

    return null;
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [TOKENS] Error extracting email from ID token: ${error}`);
    return null;
  }
}

/**
 * Clean up orphaned sessions for a user (optional cleanup utility)
 * This helps prevent accumulation of old sessions in Redis
 */
export async function cleanupOrphanedSessions(
  userEmail: string,
  currentSessionId?: string,
): Promise<void> {
  if (!(await useRedisStorage())) {
    return; // Only applicable for Redis storage
  }

  try {
    const { getRedisClient } = await import('./redis');
    const redis = getRedisClient();

    // Get all session keys
    const sessionKeys = await redis.keys(`${REDIS_SESSION_PREFIX}*`);
    const logger = getLogger();

    logger.info(`🧹 [SESSION] Found ${sessionKeys.length} total sessions in Redis`);

    let cleanedCount = 0;

    for (const key of sessionKeys) {
      try {
        const sessionData = await getRedisData(key);

        if (sessionData && sessionData.email === userEmail) {
          const sessionId = key.replace(REDIS_SESSION_PREFIX, '');

          // Don't clean up the current session
          if (currentSessionId && sessionId === currentSessionId) {
            continue;
          }

          // Check if session is expired or if we want to clean up old sessions
          const isExpired = sessionData.exp && Date.now() / 1000 > sessionData.exp;

          if (isExpired) {
            // Clean up expired session
            const cognitoKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
            await Promise.all([deleteRedisData(key), deleteRedisData(cognitoKey)]);
            cleanedCount++;

            logger.info(
              `🗑️ [SESSION] Cleaned up expired session: ${sessionId.substring(0, 10)}...`,
            );
          }
        }
      } catch (error) {
        logger.warn(`⚠️ [SESSION] Error processing session key ${key}: ${error}`);
      }
    }

    if (cleanedCount > 0) {
      logger.info(
        `✅ [SESSION] Cleaned up ${cleanedCount} orphaned/expired sessions for user: ${userEmail}`,
      );
    }
  } catch (error) {
    const logger = getLogger();
    logger.warn(`⚠️ [SESSION] Failed to cleanup orphaned sessions: ${error}`);
  }
}

/**
 * Detect if there's a token mismatch (session exists but Cognito tokens are missing)
 * This helps identify when Redis tokens have been manually deleted or expired inconsistently
 */
async function detectTokenMismatch(sessionId: string): Promise<boolean> {
  if (!(await useRedisStorage())) {
    return false; // Cannot detect mismatch without Redis
  }

  try {
    const cognitoKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
    const tokens = await getRedisData(cognitoKey);

    // If no tokens found in Redis, check if we're using Redis storage mode
    if (!tokens) {
      const logger = getLogger();
      logger.warn(
        `⚠️ [SESSION-MISMATCH] No Cognito tokens found in Redis for session ${sessionId.substring(0, 10)}...`,
      );

      // Try fallback to cookies to see if tokens exist there
      const cookieTokens = await getCognitoTokensFromCookiesOnly();

      // If no tokens in cookies either, this is a definite mismatch
      if (!cookieTokens.accessToken && !cookieTokens.idToken) {
        logger.error(
          `❌ [SESSION-MISMATCH] Complete token mismatch: session exists but no Cognito tokens found anywhere for ${sessionId.substring(0, 10)}...`,
        );
        return true;
      }

      // If tokens exist in cookies but not Redis, attempt to restore them to Redis
      if (cookieTokens.accessToken || cookieTokens.idToken) {
        logger.info(
          `🔄 [SESSION-MISMATCH] Found tokens in cookies, restoring to Redis for session ${sessionId.substring(0, 10)}...`,
        );

        try {
          await setCognitoTokenCookies(cookieTokens, sessionId);
          logger.info(
            `✅ [SESSION-MISMATCH] Successfully restored tokens to Redis for session ${sessionId.substring(0, 10)}...`,
          );
          return false; // Mismatch resolved
        } catch (error) {
          logger.error(`❌ [SESSION-MISMATCH] Failed to restore tokens to Redis: ${error}`);
          return true; // Still have a mismatch
        }
      }
    }

    // Tokens exist in Redis - verify they're valid (not empty/malformed)
    if (tokens && typeof tokens === 'object') {
      const hasValidToken = tokens.accessToken || tokens.idToken;
      if (!hasValidToken) {
        const logger = getLogger();
        logger.warn(
          `⚠️ [SESSION-MISMATCH] Redis tokens exist but are empty for session ${sessionId.substring(0, 10)}...`,
        );
        return true;
      }
    }

    return false; // No mismatch detected
  } catch (error) {
    const logger = getLogger();
    logger.error(
      `❌ [SESSION-MISMATCH] Error detecting token mismatch for session ${sessionId.substring(0, 10)}...: ${error}`,
    );
    return true; // Assume mismatch on error to be safe
  }
}

/**
 * Get Cognito tokens from cookies only (bypass Redis)
 * Used for token mismatch detection and recovery
 */
async function getCognitoTokensFromCookiesOnly(): Promise<CognitoTokens> {
  try {
    const cookieStore = await cookies();
    const accessToken = cookieStore.get(ACCESS_TOKEN_COOKIE_NAME)?.value;
    const idToken = cookieStore.get(ID_TOKEN_COOKIE_NAME)?.value;

    return { accessToken, idToken };
  } catch (error) {
    const logger = getLogger();
    logger.error(`❌ [TOKENS] Failed to get tokens from cookies only: ${error}`);
    return {};
  }
}

/**
 * Enhanced session validation that detects and handles token mismatches
 * This function can be called periodically or on critical operations
 */
export async function validateSessionConsistency(sessionId: string): Promise<{
  isValid: boolean;
  hasMismatch: boolean;
  canRecover: boolean;
  errors: string[];
}> {
  const result = {
    isValid: true,
    hasMismatch: false,
    canRecover: false,
    errors: [] as string[],
  };

  try {
    if (!(await useRedisStorage())) {
      result.errors.push('Redis not available for consistency check');
      return result;
    }

    // Check if session exists
    const sessionKey = `${REDIS_SESSION_PREFIX}${sessionId}`;
    const sessionData = await getRedisData(sessionKey);

    if (!sessionData) {
      result.isValid = false;
      result.errors.push('Session not found in Redis');
      return result;
    }

    // Check if session is expired
    if (sessionData.exp && Date.now() / 1000 > sessionData.exp) {
      result.isValid = false;
      result.errors.push('Session expired');
      return result;
    }

    // Check for token mismatch
    const tokenMismatch = await detectTokenMismatch(sessionId);
    if (tokenMismatch) {
      result.hasMismatch = true;
      result.errors.push('Cognito tokens missing or invalid');

      // Check if we can recover from cookies
      const cookieTokens = await getCognitoTokensFromCookiesOnly();
      result.canRecover = !!(cookieTokens.accessToken || cookieTokens.idToken);

      // Clear corrupted session to force re-authentication for unrecoverable mismatches
      if (!result.canRecover) {
        const logger = getLogger();
        logger.warn(
          `🗑️ [SESSION-MISMATCH] Clearing unrecoverable session: ${sessionId.substring(0, 10)}...`,
        );
        await clearSession(sessionId);
        result.isValid = false;
        result.errors.push('Session cleared due to unrecoverable token mismatch');
      }
    }

    const logger = getLogger();
    logger.info(
      `🔍 [SESSION-CONSISTENCY] Validation for ${sessionId.substring(0, 10)}...: ${JSON.stringify(result)}`,
    );
    return result;
  } catch (error) {
    result.isValid = false;
    result.errors.push(`Validation error: ${error}`);
    return result;
  }
}

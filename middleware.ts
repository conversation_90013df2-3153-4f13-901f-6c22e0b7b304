import { NextRequest, NextResponse } from 'next/server';
import { verifySessionForMiddleware } from '@/lib/session';
import { getRoutePermission, hasPermission } from '@/lib/permissions';
import { getLogger } from '@/lib/logger';

const logger = getLogger();

// Define protected routes that require authentication
const protectedRoutes = ['/devices', '/earnings', '/edit-device', '/profile', '/team'];

// Define public routes that should redirect authenticated users
const publicRoutes = ['/signin', '/signup', '/verify', '/forgot-password', '/reset-password'];

// Define OAuth callback routes that should not redirect authenticated users
const oauthCallbackRoutes = ['/oauth-callback'];

// CSRF protection is handled by individual API routes and forms

/**
 * Comprehensive middleware for authentication, session management, and security
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const response = NextResponse.next();

  // Add security headers to all responses
  addSecurityHeaders(response);

  try {
    // Enhanced session verification with roles and permissions
    const sessionCookieName =
      process.env.NODE_ENV === 'production' ? '__Secure-session' : 'session';
    const sessionCookie =
      request.cookies.get(sessionCookieName) || request.cookies.get('amplify-auth-token');

    let session = null;
    let isAuthenticated = false;

    if (sessionCookie) {
      try {
        session = await verifySessionForMiddleware(sessionCookie.value);
        isAuthenticated = !!session;
      } catch (error) {
        logger.warn(
          `Session verification failed: ${error instanceof Error ? error.message : String(error)}`,
        );
        isAuthenticated = false;
      }
    }

    // Handle protected routes with permission checking
    if (protectedRoutes.some((route) => pathname.startsWith(route))) {
      if (!isAuthenticated) {
        const signInUrl = new URL('/signin', request.url);
        signInUrl.searchParams.set('from', pathname);
        return NextResponse.redirect(signInUrl);
      }

      // Check specific route permissions only if we have a full session with permissions
      // For Redis sessions, we skip middleware permission checking and rely on server-side verification
      if (session && session.permissions.length > 0) {
        const requiredPermission = getRoutePermission(pathname);
        if (requiredPermission && !hasPermission(session.permissions, requiredPermission)) {
          logger.warn(`Access denied: User lacks permission ${requiredPermission} for ${pathname}`);
          return NextResponse.redirect(new URL('/unauthorized', request.url));
        }
      } else if (session && session.permissions.length === 0) {
        // This is a Redis session placeholder - let the server components handle permission checking
        logger.info(
          `🔍 [MIDDLEWARE] Redis session detected for ${pathname}, deferring permission check to server`,
        );
      }
    }

    // Handle public routes (redirect authenticated users)
    if (publicRoutes.some((route) => pathname.startsWith(route))) {
      if (isAuthenticated) {
        const fromParam = request.nextUrl.searchParams.get('from');
        const redirectUrl = fromParam && fromParam.startsWith('/') ? fromParam : '/';
        return NextResponse.redirect(new URL(redirectUrl, request.url));
      }
    }

    // Handle OAuth callback routes (allow access regardless of auth status)
    if (oauthCallbackRoutes.some((route) => pathname.startsWith(route))) {
      // OAuth callback should always be allowed to process
      return response;
    }

    // Handle root path
    if (pathname === '/') {
      if (!isAuthenticated) {
        return NextResponse.redirect(new URL('/signin', request.url));
      }
    }

    // Rate limiting for auth endpoints (disabled in development)
    if (
      (pathname.startsWith('/signin') ||
        pathname.startsWith('/signup') ||
        pathname.startsWith('/verify') ||
        pathname.startsWith('/forgot-password') ||
        pathname.startsWith('/reset-password')) &&
      process.env.NODE_ENV === 'production'
    ) {
      const rateLimitResponse = await handleRateLimit(request);
      if (rateLimitResponse) {
        return rateLimitResponse;
      }
    }

    return response;
  } catch (error) {
    logger.error(`Middleware error: ${error instanceof Error ? error.message : String(error)}`);

    // On error, redirect to sign-in for protected routes
    if (protectedRoutes.some((route) => pathname.startsWith(route))) {
      return NextResponse.redirect(new URL('/signin', request.url));
    }

    return response;
  }
}

/**
 * Add comprehensive security headers to all responses
 */
function addSecurityHeaders(response: NextResponse) {
  // Prevent clickjacking
  response.headers.set('X-Frame-Options', 'DENY');

  // Prevent MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff');

  // XSS protection
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // Referrer policy for privacy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Relaxed for Next.js
    "style-src 'self' 'unsafe-inline'", // Relaxed for CSS-in-JS
    "img-src 'self' data: https:",
    "connect-src 'self' https://cognito-idp.*.amazonaws.com https://api.qbraid.com ",
    "font-src 'self' data:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    'upgrade-insecure-requests',
  ].join('; ');

  response.headers.set('Content-Security-Policy', csp);

  // Strict Transport Security (HTTPS only)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set(
      'Strict-Transport-Security',
      'max-age=31536000; includeSubDomains; preload',
    );
  }

  // Permissions Policy (restrict powerful features)
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), payment=()',
  );
}

/**
 * Simple rate limiting for authentication endpoints
 */
async function handleRateLimit(request: NextRequest): Promise<NextResponse | null> {
  const ip =
    request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
  const key = `rate_limit:${ip}:${request.nextUrl.pathname}`;

  // In a production environment, you would use Redis or similar
  // For now, we'll implement a simple in-memory rate limiter
  // Note: This is not suitable for production with multiple instances

  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxAttempts = 10; // Max 10 attempts per window

  // Simple in-memory storage (replace with Redis in production)
  const attempts = getFromMemoryStore(key, now, windowMs);

  if (attempts >= maxAttempts) {
    const response = new NextResponse('Too Many Requests', { status: 429 });
    response.headers.set('Retry-After', '900'); // 15 minutes
    return response;
  }

  // Increment attempt counter
  setInMemoryStore(key, attempts + 1, now + windowMs);

  return null;
}

// Simple in-memory storage for rate limiting
// In production, replace with Redis or similar distributed cache
const memoryStore = new Map<string, { count: number; expiry: number }>();

function getFromMemoryStore(key: string, now: number, _windowMs: number): number {
  const entry = memoryStore.get(key);
  if (!entry || entry.expiry < now) {
    return 0;
  }
  return entry.count;
}

function setInMemoryStore(key: string, count: number, expiry: number): void {
  memoryStore.set(key, { count, expiry });

  // Clean up expired entries periodically
  if (Math.random() < 0.01) {
    // 1% chance
    const now = Date.now();
    for (const [k, v] of memoryStore.entries()) {
      if (v.expiry < now) {
        memoryStore.delete(k);
      }
    }
  }
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};

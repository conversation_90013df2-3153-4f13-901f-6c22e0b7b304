#!/usr/bin/env node

/**
 * Remove Unused Dependencies Script
 *
 * This script removes dependencies that are no longer used in the codebase
 * after the cleanup of demo files and redundant components.
 */

const { execSync } = require('child_process');

// Simple logger for scripts
const logger = {
  info: (message, ...args) => console.log(message, ...args),
  warn: (message, ...args) => console.warn(message, ...args),
  error: (message, ...args) => console.error(message, ...args),
};

// Dependencies that are likely unused after cleanup
const potentiallyUnusedDeps = [
  // Three.js related (only used in one UI component that might be removed)
  '@react-three/fiber',
  'three',
  '@types/three',

  // DnD Kit (only used in data-table which might be simplified)
  '@dnd-kit/core',
  '@dnd-kit/modifiers',
  '@dnd-kit/sortable',
  '@dnd-kit/utilities',

  // Formik/Yup (only used in edit-device-form)
  'formik',
  'yup',

  // PapaParse (only used in invite-member-button)
  'papaparse',
  '@types/papaparse',

  // Motion (might be replaced with simpler animations)
  'motion',

  // UUID (can be replaced with crypto.randomUUID)
  'uuid',

  // Vaul (drawer component that might not be used)
  'vaul',
];

logger.info('🔍 Checking for unused dependencies...');

// Function to check if a dependency is actually used
function isDependencyUsed(dep) {
  try {
    // Check for imports in TypeScript/JavaScript files
    const result = execSync(
      `grep -r "from '${dep}'" --include="*.tsx" --include="*.ts" . || true`,
      {
        encoding: 'utf8',
        stdio: 'pipe',
      },
    );

    // Also check for require statements
    const requireResult = execSync(
      `grep -r "require('${dep}')" --include="*.tsx" --include="*.ts" --include="*.js" . || true`,
      {
        encoding: 'utf8',
        stdio: 'pipe',
      },
    );

    // Filter out node_modules results
    const filteredResult = result
      .split('\n')
      .filter((line) => line.trim() && !line.includes('node_modules'))
      .join('\n');

    const filteredRequireResult = requireResult
      .split('\n')
      .filter((line) => line.trim() && !line.includes('node_modules'))
      .join('\n');

    return filteredResult.trim().length > 0 || filteredRequireResult.trim().length > 0;
  } catch (error) {
    logger.warn(`⚠️  Error checking dependency ${dep}:`, error.message);
    return true; // Assume it's used if we can't check
  }
}

const unusedDeps = [];
const usedDeps = [];

for (const dep of potentiallyUnusedDeps) {
  logger.info(`🔍 Checking ${dep}...`);

  if (isDependencyUsed(dep)) {
    logger.info(`✅ ${dep} is being used`);
    usedDeps.push(dep);
  } else {
    logger.info(`❌ ${dep} appears to be unused`);
    unusedDeps.push(dep);
  }
}

logger.info('\n📊 Analysis Results:');
logger.info(`✅ Used dependencies: ${usedDeps.length}`);
logger.info(`❌ Unused dependencies: ${unusedDeps.length}`);

if (unusedDeps.length > 0) {
  logger.info('\n🗑️  Unused dependencies found:');
  unusedDeps.forEach((dep) => logger.info(`   - ${dep}`));

  logger.info('\n💡 To remove these dependencies, run:');
  logger.info(`npm uninstall ${unusedDeps.join(' ')}`);

  // Ask for confirmation before removing
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  rl.question('\n❓ Do you want to remove these unused dependencies? (y/N): ', (answer) => {
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      logger.info('\n🗑️  Removing unused dependencies...');

      try {
        execSync(`npm uninstall ${unusedDeps.join(' ')}`, { stdio: 'inherit' });
        logger.info('\n✅ Successfully removed unused dependencies!');
      } catch (error) {
        logger.error('\n❌ Error removing dependencies:', error.message);
      }
    } else {
      logger.info('\n⏭️  Skipping dependency removal.');
    }

    rl.close();
  });
} else {
  logger.info('\n✨ No unused dependencies found!');
}
